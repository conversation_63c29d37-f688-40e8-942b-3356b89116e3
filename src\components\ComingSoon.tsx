import { Bell, CheckCircle, Zap, Clock, Star, ArrowRight } from 'lucide-react';

const ComingSoon = () => {
  // const [timeLeft, setTimeLeft] = useState({
  //   days: 0,
  //   hours: 0,
  //   minutes: 0,
  //   seconds: 0
  // });

  // Set launch date (90 days from now)
  // const launchDate = useMemo(() => {
  //   const date = new Date();
  //   date.setDate(date.getDate() + 90);
  //   return date;
  // }, []);

  const milestones = [
    {
      date: undefined,
      title: 'Beta Platform Launch',
      description: 'Limited beta release with select partners',
      completed: true,
      current: false
    },
    {
      date: undefined,
      title: 'Mobile Apps Release',
      description: 'iOS and Android applications',
      completed: false,
      current: true
    },
    {
      date: undefined,
      title: 'Multi-Language Support',
      description: 'Regional language integration',
      completed: false,
      current: false
    },
    {
      date: undefined,
      title: 'Full Platform Launch',
      description: 'Public release with all core features',
      completed: false,
      current: false
    }
  ];

  // useEffect(() => {
  //   const timer = setInterval(() => {
  //     const now = new Date().getTime();
  //     const distance = launchDate.getTime() - now;

  //     if (distance > 0) {
  //       setTimeLeft({
  //         days: Math.floor(distance / (1000 * 60 * 60 * 24)),
  //         hours: Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
  //         minutes: Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60)),
  //         seconds: Math.floor((distance % (1000 * 60)) / 1000)
  //       });
  //     }
  //   }, 1000);

  //   return () => clearInterval(timer);
  // }, [launchDate]);

  return (
    <section id="coming-soon" className="py-20 bg-[#FFFFFF] relative overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Title */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-6xl font-bold text-[#000000] mb-6 leading-tight">
            Next-Gen Platform
            <span className="block gradient-text">Coming Soon</span>
          </h2>
          <p className="text-xl text-[#000000] max-w-3xl mx-auto leading-relaxed">
            We're building the most advanced agricultural technology platform.
            Be the first to experience revolutionary farming solutions that will transform your agricultural operations.
          </p>
        </div>
        {/* Countdown Timer */}
        {/* <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-12 max-w-2xl mx-auto">
            {[
              { label: 'Days', value: timeLeft.days },
              { label: 'Hours', value: timeLeft.hours },
              { label: 'Minutes', value: timeLeft.minutes },
              { label: 'Seconds', value: timeLeft.seconds }
            ].map((item, index) => (
              <div
                key={index}
                className="flex flex-col items-center justify-center  bg-agri-cream rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow duration-300"
              >
                <div className="text-3xl sm:text-4xl font-bold text-agri-green mb-2">
                  {item.value.toString().padStart(2, '0')}
                </div>
                <div className="text-sm font-medium text-gray-600 uppercase tracking-wide">
                  {item.label}
                </div>
              </div>
            ))}
          </div> */}
      </div>

      {/* Development Roadmap */}
      <div className="mb-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <div className="relative">
              {/* Timeline Line */}
              <div className="absolute left-6 sm:left-8 top-0 bottom-0 w-0.5 bg-slate-300"></div>
              {/* Progress Line for Completed Milestones */}
              <div
                className="absolute left-6 sm:left-8 top-0 w-0.5 bg-gradient-to-b from-[#2D4D31] to-[#1F3322] transition-all duration-1000 ease-out"
                style={{
                  height: `${(milestones.filter(m => m.completed).length / milestones.length) * 100}%`
                }}
              ></div>
              {/* Current Progress Line */}
              <div
                className="absolute left-6 sm:left-8 w-0.5 bg-gradient-to-b from-[#D36C3C] to-[#B85A32] transition-all duration-1000 ease-out"
                style={{
                  top: `${(milestones.filter(m => m.completed).length / milestones.length) * 100}%`,
                  height: `${milestones.some(m => m.current) ? (1 / milestones.length) * 50 : 0}%`
                }}
              ></div>
              
              <div className="space-y-8">
                {milestones.map((milestone, index) => (
                  <div key={index} className={`relative flex items-start transition-all duration-500 ${
                    milestone.completed ? 'animate-slide-in' : milestone.current ? 'animate-pulse-once' : 'animate-fade-in-up'
                  }`}>
                    {/* Timeline Dot */}
                    <div className={`relative z-10 w-12 h-12 sm:w-16 sm:h-16 rounded-full flex items-center justify-center transition-all duration-300 group flex-shrink-0 ${
                      milestone.completed
                        ? 'bg-gradient-to-br from-[#2D4D31] to-[#1F3322] shadow-lg shadow-[#2D4D31]/30 ring-4 ring-[#2D4D31]/20 animate-glow'
                        : milestone.current
                        ? 'bg-gradient-to-br from-[#D36C3C] to-[#B85A32] shadow-lg shadow-[#D36C3C]/40 ring-4 ring-[#D36C3C]/20'
                        : 'bg-gradient-to-br from-slate-100 to-slate-200 border-4 border-slate-300 hover:border-[#D36C3C] hover:shadow-lg hover:shadow-[#D36C3C]/30 hover:from-[#FFF5EA] hover:to-white'
                    }`}>
                      {milestone.completed ? (
                        <CheckCircle className="h-8 w-8 text-white drop-shadow-sm" />
                      ) : milestone.current ? (
                        <Zap className="h-8 w-8 text-white drop-shadow-sm" />
                      ) : (
                        <Star className="h-7 w-7 text-slate-500 group-hover:text-[#D36C3C] transition-colors duration-300" />
                      )}
                    </div>

                    {/* Content */}
                    <div className="ml-6 sm:ml-8 flex-1">
                      <div className={`rounded-2xl p-4 sm:p-6 transition-all duration-300 group ${
                        milestone.completed
                          ? 'bg-gradient-to-r from-[#FFF5EA] to-white border-2 border-[#2D4D31]/20 shadow-md hover:shadow-lg'
                          : milestone.current
                          ? 'bg-gradient-to-r from-[#FFF5EA] to-white border-2 border-[#D36C3C]/30 shadow-md hover:shadow-lg ring-2 ring-[#D36C3C]/20'
                          : 'bg-gradient-to-r from-slate-50 to-white border-2 border-slate-200 hover:border-[#D36C3C]/30 hover:shadow-xl hover:shadow-[#D36C3C]/10 hover:from-[#FFF5EA] hover:to-white'
                      }`}>
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-3">
                            <h4 className={`text-lg font-bold transition-colors duration-300 ${
                              milestone.completed ? 'text-[#2D4D31]' : milestone.current ? 'text-[#D36C3C]' : 'text-[#000000] group-hover:text-[#D36C3C]'
                            }`}>
                              {milestone.title}
                            </h4>
                            {milestone.completed && (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-[#2D4D31]/10 text-[#2D4D31] border border-[#2D4D31]/20">
                                ✓ Completed
                              </span>
                            )}
                            {milestone.current && (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-[#D36C3C]/10 text-[#D36C3C] border border-[#D36C3C]/20 animate-pulse">
                                <Clock className="w-3 h-3 mr-1" />
                                In Progress
                              </span>
                            )}
                            {!milestone.completed && !milestone.current && (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-slate-100 text-slate-600 border border-slate-200 group-hover:bg-[#D36C3C]/10 group-hover:text-[#D36C3C] group-hover:border-[#D36C3C]/20 transition-all duration-300">
                                <ArrowRight className="w-3 h-3 mr-1" />
                                Coming Soon
                              </span>
                            )}
                          </div>
                          {milestone.date && (
                            <span className={`text-sm font-medium transition-colors duration-300 ${
                              milestone.completed ? 'text-[#2D4D31]' : milestone.current ? 'text-[#D36C3C]' : 'text-slate-500 group-hover:text-[#D36C3C]'
                            }`}>
                              {milestone.date}
                            </span>
                          )}
                        </div>
                        <p className={`transition-colors duration-300 ${
                          milestone.completed ? 'text-[#2D4D31]' : milestone.current ? 'text-[#D36C3C]' : 'text-slate-600 group-hover:text-[#D36C3C]'
                        }`}>
                          {milestone.description}
                        </p>
                        {milestone.completed && (
                          <div className="mt-3 flex items-center text-sm text-[#2D4D31]">
                            <div className="w-2 h-2 bg-[#2D4D31] rounded-full mr-2 animate-pulse"></div>
                            Successfully delivered
                          </div>
                        )}
                        {milestone.current && (
                          <div className="mt-3 flex items-center text-sm text-[#D36C3C]">
                            <div className="w-2 h-2 bg-[#D36C3C] rounded-full mr-2 animate-pulse"></div>
                            Currently in development
                          </div>
                        )}
                        {!milestone.completed && !milestone.current && (
                          <div className="mt-3 flex items-center text-sm text-slate-500 group-hover:text-blue-600 transition-colors duration-300">
                            <div className="w-2 h-2 bg-slate-400 rounded-full mr-2 group-hover:bg-blue-500 transition-colors duration-300"></div>
                            Planned for future release
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

      {/* Notification Signup */}
      <div className="max-w-2xl mx-auto bg-[#FFF5EA] p-8 rounded-xl border border-[#2D4D31]/10">
          <div className="flex items-center justify-center mb-6">
            <Bell className="w-8 h-8 text-[#2D4D31] mr-3" />
            <h3 className="text-2xl font-semibold text-[#000000]">Get Notified</h3>
          </div>

          <p className="text-center text-[#000000] opacity-80 mb-6">
            Be the first to know when AgriTram launches. Join our exclusive waitlist.
          </p>

          <div className="flex flex-col sm:flex-row gap-4">
            <input
              type="email"
              placeholder="Enter your email address"
              className="flex-1 px-6 py-3 rounded-lg border-2 border-[#2D4D31] border-opacity-30 focus:border-[#2D4D31] focus:outline-none bg-[#FFFFFF] text-[#000000]"
            />
            <button className="bg-[#2D4D31] text-[#FFFFFF] px-8 py-3 rounded-lg border-2 border-[#2D4D31] border-opacity-70 border-dotted hover:bg-[#1F3322] hover:border-[#1F3322] transition-all duration-300">
              Notify Me
            </button>
          </div>
          <p className="text-sm text-center mt-2 text-[#000000]/70">
            Join 1,000+ farmers, 10+ traders and 10+ manufacturers already signed up for early access
          </p>
        </div>

      {/* Background Decoration */}
      <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-bl from-[#2D4D31]/20 to-transparent rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-gradient-to-tr from-[#D36C3C]/20 to-transparent rounded-full blur-3xl"></div>
    </section>
  );
};

export default ComingSoon;