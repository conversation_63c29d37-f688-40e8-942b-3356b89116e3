@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  }

  html {
    scroll-behavior: smooth;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  }

  body {
    line-height: 1.6;
    font-weight: 400;
    color: #000000; /* Theme primary text color */
    background-color: #FFF5EA; /* Theme main background color */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Enhanced Typography */
  h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    color: #000000; /* Theme primary text color */
  }

  h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    letter-spacing: -0.02em;
  }

  h2 {
    font-size: clamp(2rem, 4vw, 3rem);
    letter-spacing: -0.01em;
  }

  h3 {
    font-size: clamp(1.5rem, 3vw, 2rem);
  }

  p {
    color: #000000; /* Theme primary text color */
    line-height: 1.7;
  }

  /* Links */
  a {
    color: #D36C3C; /* Theme link color */
    text-decoration: none;
    transition: color 0.2s ease;
  }

  a:hover {
    color: #B85A32; /* Theme link hover color */
  }

  /* Focus styles for accessibility */
  *:focus-visible {
    outline: 2px solid #2D4D31; /* Theme button color for focus */
    outline-offset: 2px;
    border-radius: 4px;
  }
}

/* Enhanced Custom Scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #FFF5EA; /* Theme main background */
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #2D4D31, #1F3322); /* Theme button colors */
  border-radius: 3px;
  transition: all 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #1F3322, #1A2B1D); /* Darker theme button colors */
  box-shadow: 0 0 8px rgba(45, 77, 49, 0.3);
}

/* Enhanced Custom Animations */
@keyframes pulse-once {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 8px rgba(45, 77, 49, 0.4);
  }
  50% {
    box-shadow: 0 0 25px rgba(45, 77, 49, 0.7), 0 0 35px rgba(45, 77, 49, 0.3);
  }
}

@keyframes slideInFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes breathe {
  0%, 100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.02);
    opacity: 1;
  }
}

.animate-pulse-once {
  animation: pulse-once 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-glow {
  animation: glow 3s ease-in-out infinite;
}

.animate-slide-in {
  animation: slideInFromLeft 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-breathe {
  animation: breathe 4s ease-in-out infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-6px);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInRight {
  0% {
    opacity: 0;
    transform: translateX(30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
  background-size: 200% 100%;
  animation: shimmer 2.5s infinite;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-scale-in {
  animation: scaleIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-slide-in-right {
  animation: slideInRight 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Utility Classes */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .gradient-text {
    background: linear-gradient(135deg, #2D4D31, #D36C3C); /* Theme button to link gradient */
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .gradient-text-green {
    background: linear-gradient(135deg, #2D4D31, #1F3322); /* Theme button gradient */
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .gradient-text-orange {
    background: linear-gradient(135deg, #D36C3C, #B85A32); /* Theme link gradient */
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Enhanced Color Variations */
  .bg-theme-gradient {
    background: linear-gradient(135deg, #FFF5EA 0%, #FFFFFF 50%, #FFF5EA 100%);
  }

  .bg-theme-gradient-reverse {
    background: linear-gradient(135deg, #FFFFFF 0%, #FFF5EA 50%, #FFFFFF 100%);
  }

  .bg-theme-subtle {
    background: linear-gradient(135deg, #FFF5EA 0%, #FFEDE0 100%);
  }

  .border-theme-gradient {
    border-image: linear-gradient(90deg, #2D4D31, #D36C3C, #2D4D31) 1;
  }

  .text-theme-muted {
    color: rgba(0, 0, 0, 0.7);
  }

  .text-theme-light {
    color: rgba(0, 0, 0, 0.6);
  }

  .glass-effect {
    background: rgba(255, 245, 234, 0.1); /* Theme main background with transparency */
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 245, 234, 0.2);
  }

  .glass-effect-white {
    background: rgba(255, 255, 255, 0.1); /* White with transparency */
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .shadow-glow-green {
    box-shadow: 0 0 20px rgba(45, 77, 49, 0.3); /* Theme button color glow */
  }

  .shadow-glow-orange {
    box-shadow: 0 0 20px rgba(211, 108, 60, 0.3); /* Theme link color glow */
  }

  .shadow-theme-soft {
    box-shadow: 0 2px 15px -3px rgba(45, 77, 49, 0.07), 0 10px 20px -2px rgba(45, 77, 49, 0.04);
  }

  .shadow-theme-medium {
    box-shadow: 0 4px 25px -5px rgba(45, 77, 49, 0.1), 0 10px 10px -5px rgba(45, 77, 49, 0.04);
  }

  .shadow-theme-strong {
    box-shadow: 0 10px 40px -10px rgba(45, 77, 49, 0.15), 0 2px 10px -2px rgba(45, 77, 49, 0.05);
  }
}

/* Component Styles */
@layer components {
  .btn-primary {
    @apply bg-[#2D4D31] hover:bg-[#1F3322] text-[#FFFFFF] font-semibold py-3 px-6 rounded-xl transition-all duration-300 shadow-medium hover:shadow-glow-green border-2 border-[#2D4D31] hover:border-[#1F3322];
  }

  .btn-secondary {
    @apply bg-[#FFFFFF] text-[#2D4D31] border-2 border-[#2D4D31] hover:bg-[#2D4D31] hover:text-[#FFFFFF] font-semibold py-3 px-6 rounded-xl transition-all duration-300 shadow-soft hover:shadow-medium;
  }

  .btn-outline {
    @apply bg-transparent text-[#2D4D31] border-2 border-[#2D4D31] hover:bg-[#2D4D31] hover:text-[#FFFFFF] font-semibold py-3 px-6 rounded-xl transition-all duration-300;
  }

  .btn-link {
    @apply bg-transparent text-[#D36C3C] hover:text-[#B85A32] font-semibold py-2 px-4 rounded-lg transition-all duration-300 hover:bg-[#FFF5EA];
  }

  .card {
    @apply bg-[#FFFFFF] rounded-2xl shadow-soft hover:shadow-medium transition-all duration-300 border border-neutral-200;
  }

  .card-elevated {
    @apply bg-[#FFFFFF] rounded-2xl shadow-medium hover:shadow-strong transition-all duration-300 border border-neutral-200;
  }

  .card-cream {
    @apply bg-[#FFF5EA] rounded-2xl shadow-soft hover:shadow-medium transition-all duration-300 border border-neutral-200;
  }

  /* Enhanced Interactive Elements */
  .btn-theme-gradient {
    background: linear-gradient(135deg, #2D4D31 0%, #1F3322 100%);
    color: #FFFFFF;
    border: 2px solid transparent;
    background-clip: padding-box;
    transition: all 0.3s ease;
  }

  .btn-theme-gradient:hover {
    background: linear-gradient(135deg, #1F3322 0%, #1A2B1D 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(45, 77, 49, 0.4);
  }

  .btn-theme-outline-gradient {
    background: transparent;
    color: #2D4D31;
    border: 2px solid;
    border-image: linear-gradient(135deg, #2D4D31, #D36C3C) 1;
    transition: all 0.3s ease;
  }

  .btn-theme-outline-gradient:hover {
    background: linear-gradient(135deg, #2D4D31, #D36C3C);
    color: #FFFFFF;
    transform: translateY(-2px);
  }

  /* Responsive Alignment Utilities */
  .section-spacing {
    @apply py-16 sm:py-20 lg:py-24;
  }

  .container-padding {
    @apply px-4 sm:px-6 lg:px-8;
  }

  .max-content-width {
    @apply max-w-7xl mx-auto;
  }

  .text-responsive-xl {
    @apply text-lg sm:text-xl lg:text-2xl;
  }

  .text-responsive-2xl {
    @apply text-xl sm:text-2xl lg:text-3xl;
  }

  .text-responsive-3xl {
    @apply text-2xl sm:text-3xl lg:text-4xl;
  }

  .grid-responsive-2 {
    @apply grid grid-cols-1 lg:grid-cols-2;
  }

  .grid-responsive-3 {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3;
  }

  .gap-responsive {
    @apply gap-6 sm:gap-8 lg:gap-12;
  }
}