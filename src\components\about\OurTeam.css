/* About Us Section Styles */
.about-section {
  padding: 6rem 0;
  background: linear-gradient(135deg, #FFF5EA 0%, #FFFFFF 50%, #FFF5EA 100%); /* Theme backgrounds */
}

.about-section .container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.about-section__header {
  text-align: center;
  margin-bottom: 5rem;
}

.about-section__title {
  font-size: clamp(2.25rem, 4vw, 3.5rem);
  font-weight: 800;
  color: #000000; /* Theme primary text */
  margin-bottom: 1.5rem;
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, #2D4D31, #1F3322); /* Theme button gradient */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.about-section__description {
  font-size: 1.125rem;
  line-height: 1.75;
  color: #000000; /* Theme primary text */
  max-width: 700px;
  margin: 0 auto;
  font-weight: 400;
}

/* Team Grid */
.team-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 2.5rem;
  margin-bottom: 5rem;
  justify-content: center;
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
}

.team-card {
  background: linear-gradient(135deg, #FFFFFF 0%, #FFF5EA 100%); /* Theme alt to main background */
  border-radius: 20px;
  padding: 2.5rem;
  text-align: center;
  box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(45, 77, 49, 0.1); /* Theme button color border */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.team-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #2D4D31, #D36C3C, #2D4D31); /* Theme button to link gradient */
  border-radius: 20px 20px 0 0;
}

.team-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.15), 0 2px 10px -2px rgba(0, 0, 0, 0.05);
  border-color: rgba(45, 77, 49, 0.3); /* Theme button color border */
}

.team-card__image {
  position: relative;
  width: 140px;
  height: 140px;
  margin: 0 auto 2rem;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(45, 77, 49, 0.2);
  border: 4px solid rgba(45, 77, 49, 0.1);
}

.team-card__image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.team-card:hover .team-card__image img {
  transform: scale(1.1);
}

.team-card__social {
  position: absolute;
  bottom: 8px;
  right: 8px;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #D36C3C, #B85A32); /* Theme link colors */
  color: #FFFFFF; /* Theme button text */
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  box-shadow: 0 4px 15px rgba(211, 108, 60, 0.3);
}

.team-card__social:hover {
  background: linear-gradient(135deg, #2D4D31, #1F3322); /* Theme button colors */
  transform: scale(1.15);
  box-shadow: 0 6px 20px rgba(45, 77, 49, 0.4);
}

.team-card__name {
  font-size: 1.375rem;
  font-weight: 700;
  color: #000000; /* Theme primary text */
  margin-bottom: 0.5rem;
  letter-spacing: -0.01em;
}

.team-card__position {
  font-size: 1rem;
  font-weight: 600;
  color: #D36C3C; /* Theme link color */
  margin-bottom: 1.25rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 0.875rem;
}

.team-card__bio {
  font-size: 0.9375rem;
  line-height: 1.6;
  color: #000000; /* Theme primary text */
  font-weight: 400;
}
  
/* Advisors Section */
.advisors-section {
  margin-bottom: 5rem;
}

.advisors-title {
  font-size: 1.875rem;
  font-weight: 700;
  color: #000000; /* Theme primary text */
  text-align: center;
  margin-bottom: 2.5rem;
  background: linear-gradient(135deg, #2D4D31, #D36C3C); /* Theme button to link gradient */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.advisors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.advisor-card {
  background: linear-gradient(135deg, #FFFFFF 0%, #FFF5EA 100%); /* Theme alt to main background */
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  border: 2px solid rgba(45, 77, 49, 0.1); /* Theme button color border */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
}

.advisor-card:hover {
  border-color: rgba(45, 77, 49, 0.3); /* Theme button color border */
  transform: translateY(-4px);
  box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.advisor-name {
  font-size: 1.125rem;
  font-weight: 700;
  color: #000000; /* Theme primary text */
  margin-bottom: 0.5rem;
}

.advisor-title {
  font-size: 1rem;
  font-weight: 600;
  color: #D36C3C; /* Theme link color */
  margin-bottom: 0.5rem;
}

.advisor-company {
  font-size: 0.875rem;
  color: #000000; /* Theme primary text */
  opacity: 0.8;
}

/* Mission Section */
.about-section__mission {
  background: linear-gradient(135deg, #2D4D31 0%, #1F3322 50%, #1A2B1D 100%); /* Theme button colors */
  border-radius: 24px;
  padding: 4rem 3rem;
  color: #FFFFFF; /* Theme button text */
  text-align: center;
  position: relative;
  overflow: hidden;
  margin-top: 4rem;
  box-shadow: 0 10px 40px -10px rgba(45, 77, 49, 0.3);
}

.about-section__mission::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.08) 0%, transparent 70%);
  animation: rotate 20s linear infinite;
}

.mission-content {
  position: relative;
  z-index: 1;
  margin-bottom: 3rem;
}

.mission-title {
  font-size: clamp(1.875rem, 4vw, 2.5rem);
  font-weight: 800;
  margin-bottom: 1.5rem;
  letter-spacing: -0.01em;
  color: #FFFFFF; /* Theme button text */
}

.mission-text {
  font-size: 1.125rem;
  line-height: 1.75;
  max-width: 800px;
  margin: 0 auto;
  opacity: 0.95;
  font-weight: 400;
  color: #FFFFFF; /* Theme button text */
}

.mission-stats {
  display: flex;
  justify-content: center;
  gap: 3rem;
  flex-wrap: wrap;
  position: relative;
  z-index: 1;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: clamp(1.875rem, 4vw, 2.5rem);
  font-weight: 800;
  margin-bottom: 0.5rem;
  color: #FFFFFF; /* Theme button text */
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-label {
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  opacity: 0.9;
  font-weight: 500;
  color: #FFFFFF; /* Theme button text */
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
  
/* Responsive Design */
@media (max-width: 767px) {
  .about-section {
    padding: 4rem 0;
  }

  .about-section .container {
    padding: 0 1rem;
  }

  .about-section__header {
    margin-bottom: 3rem;
  }

  .team-grid {
    grid-template-columns: 1fr;
    grid-template-rows: auto;
    gap: 2rem;
    max-width: 400px;
  }

  .team-card {
    padding: 2rem;
  }

  .team-card__image {
    width: 120px;
    height: 120px;
  }

  .advisors-grid {
    grid-template-columns: 1fr;
  }

  .about-section__mission {
    padding: 3rem 1.5rem;
    margin-top: 3rem;
  }

  .mission-stats {
    gap: 2rem;
  }
}

@media (min-width: 768px) {
  .team-grid {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(3, 1fr);
    max-width: 600px;
  }

  .advisors-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .about-section {
    padding: 8rem 0;
  }

  .team-grid {
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 3rem;
    max-width: 1000px;
  }

  .advisors-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) {
  .about-section .container {
    padding: 0 2rem;
  }

  .team-grid {
    gap: 3.5rem;
  }

  .team-card {
    padding: 3rem;
  }

  .team-card__image {
    width: 160px;
    height: 160px;
  }
}

/* Enhanced Animations */
.team-card {
  animation: fadeInUp 0.6s ease-out;
}

.team-card:nth-child(1) { animation-delay: 0.1s; }
.team-card:nth-child(2) { animation-delay: 0.2s; }
.team-card:nth-child(3) { animation-delay: 0.3s; }
.team-card:nth-child(4) { animation-delay: 0.4s; }

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Focus States for Accessibility */
.team-card__social:focus-visible {
  outline: 2px solid #3a9b47;
  outline-offset: 2px;
  border-radius: 50%;
}

/* Print Styles */
@media print {
  .about-section {
    padding: 2rem 0;
  }

  .team-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #e5e5e5;
  }

  .team-card__social {
    display: none;
  }

  .about-section__mission {
    background: #f5f5f5 !important;
    color: #000 !important;
  }
}
  