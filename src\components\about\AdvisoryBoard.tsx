import React from 'react';
import './OurTeam.css';

interface AdvisorMember {
  id: number;
  name: string;
  position: string;
  company: string;
  image: string;
  linkedin?: string;
}

const AdvisoryBoard: React.FC = () => {
  const advisors: AdvisorMember[] = [
    {
      id: 1,
      name: "Dr. <PERSON>",
      position: "Former Director",
      company: "USDA Agricultural Research Service",
      image: "https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
      linkedin: "#"
    },
    {
      id: 2,
      name: "<PERSON>",
      position: "VP of Innovation",
      company: "<PERSON>",
      image: "https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
      linkedin: "#"
    },
    {
      id: 3,
      name: "<PERSON>",
      position: "Blockchain Consultant",
      company: "Former Ethereum Foundation",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
      linkedin: "#"
    }
  ];


  return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-20">
        <div className="advisors-section">
          <h2 className="advisors-title">
            Advisory Board
          </h2>

          <div className="advisors-grid">
            {advisors.map((advisor) => (
              <div key={advisor.id} className="advisor-card">
                <h3 className="advisor-name">{advisor.name}</h3>
                <p className="advisor-title">{advisor.position}</p>
                <p className="advisor-company">{advisor.company}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
  );
};

export default AdvisoryBoard;
